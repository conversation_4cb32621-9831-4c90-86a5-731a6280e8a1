import 'package:flutter/material.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';

/// Payment summary row
class SummaryRow extends StatelessWidget {
  /// Constructor
  const SummaryRow({
    required this.title,
    required this.amount,
    this.isSubItem = false,
    this.isNetAmount = false,
    super.key,
  });

  /// Title
  final String title;

  /// Amount
  final String amount;

  /// Whether this is a sub-item (indented)
  final bool isSubItem;

  /// Whether this is a net amount (highlighted)
  final bool isNetAmount;

  @override
  Widget build(BuildContext context) {
    return amount.isEmpty
        ? const SizedBox()
        : Padding(
            padding: EdgeInsets.only(
              top: AppSize.h8,
              left: isSubItem ? AppSize.w16 : 0,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              spacing: AppSize.w10,
              children: [
                Flexible(
                  child: Text(
                    title,
                    style: context.textTheme.bodyMedium?.copyWith(
                      fontWeight: isNetAmount
                          ? FontWeight.w600
                          : FontWeight.w500,
                      fontSize: isSubItem ? AppSize.sp13 : AppSize.sp14,
                      color: isNetAmount
                          ? AppColors.primaryColor
                          : isSubItem
                          ? AppColors.ff6C757D
                          : null,
                    ),
                  ),
                ),
                Text(
                  amount,
                  style: context.textTheme.bodyMedium?.copyWith(
                    fontSize: isSubItem ? AppSize.sp14 : AppSize.sp16,
                    fontWeight: isNetAmount ? FontWeight.w600 : FontWeight.w400,
                    color: isNetAmount ? AppColors.primaryColor : null,
                  ),
                ),
              ],
            ),
          );
  }
}

/// Detailed payment breakdown widget
class DetailedPaymentBreakdown extends StatelessWidget {
  /// Constructor
  const DetailedPaymentBreakdown({
    required this.title,
    required this.grossAmount,
    required this.netAmount,
    this.showGrossAmount = true,
    super.key,
  });

  /// Title of the breakdown section
  final String title;

  /// Gross amount
  final String grossAmount;

  /// Net amount (what customer pays)
  final String netAmount;

  /// Whether to show gross amount
  final bool showGrossAmount;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SummaryRow(title: title, amount: ''),
        if (showGrossAmount && grossAmount.isNotEmpty)
          SummaryRow(
            title: '• Gross Amount',
            amount: grossAmount,
            isSubItem: true,
          ),
        if (netAmount.isNotEmpty)
          SummaryRow(
            title: '• Net Amount (You Pay)',
            amount: netAmount,
            isSubItem: true,
            isNetAmount: true,
          ),
      ],
    );
  }
}
