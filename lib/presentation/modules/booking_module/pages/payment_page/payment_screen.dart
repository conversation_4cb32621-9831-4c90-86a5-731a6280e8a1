import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/num_extension.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/provider/payment_provider.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/widgets/payment_summary_widget.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// Payment page
class PaymentScreen extends StatelessWidget {
  /// Constructor
  const PaymentScreen({
    super.key,
    required this.isExclusiveTrip,
    required this.bookingId,
    required this.paymentDataModel,
    required this.bookingProviderData,
  }) : assert(
         bookingProviderData != null || paymentDataModel != null,
         'Please pass payment and booking data',
       );
  final bool isExclusiveTrip;
  final String? bookingId;
  final PaymentDataModel? paymentDataModel;
  final ProviderData? bookingProviderData;

  /// Helper method to generate transportation cost details
  List<PaymentDetail> _getTransportationDetails(
    BuildContext context,
    PaymentSummaryModel paymentSummary,
  ) {
    final details = <PaymentDetail>[];

    // Add base rate information
    if (paymentSummary.transportationCharge != null) {
      details.add(
        PaymentDetail(
          label: context.l10n.baseRate,
          value: paymentSummary.transportationCharge!.toString().smartFormat(),
        ),
      );
    }

    // // Add discount information if applicable
    // if (paymentSummary.transportationCharge != null &&
    //     paymentSummary.netPayTransportationCharge != null) {
    //   final discount =
    //       paymentSummary.transportationCharge! -
    //       paymentSummary.netPayTransportationCharge!;
    //   if (discount > 0) {
    //     details.add(
    //       PaymentDetail(
    //         label: context.l10n.discountApplied,
    //         value: '-${discount.toString().smartFormat()}',
    //       ),
    //     );
    //   }
    // }

    return details;
  }

  /// Helper method to generate storage cost details
  List<PaymentDetail> _getStorageDetails(
    BuildContext context,
    num? grossAmount,
    num? netAmount,
  ) {
    final details = <PaymentDetail>[];

    if (grossAmount != null && netAmount != null) {
      // Calculate estimated days (this is a simplified calculation)
      final estimatedDays = (grossAmount / 100)
          .round(); // Assuming $100 per day
      details..add(
        PaymentDetail(
          label: context.l10n.totalDays,
          value: '$estimatedDays days',
        ),
      )

      ..add(
        PaymentDetail(label: context.l10n.perDayRate, value: r'$100.00'),
      );

      if (grossAmount != netAmount) {
        final discount = grossAmount - netAmount;
        details.add(
          PaymentDetail(
            label: context.l10n.discountApplied,
            value: '-${discount.toString().smartFormat()}',
          ),
        );
      }
    }

    return details;
  }

  /// Helper method to generate insurance details
  List<PaymentDetail> _getInsuranceDetails(
    BuildContext context,
    num? grossAmount,
    num? netAmount,
  ) {
    final details = <PaymentDetail>[];

    if (grossAmount != null) {
      details.add(
        const PaymentDetail(label: 'Coverage Type', value: 'Basic Protection'),
      );

      if (netAmount != null && grossAmount != netAmount) {
        final discount = grossAmount - netAmount;
        details.add(
          PaymentDetail(
            label: context.l10n.discountApplied,
            value: '-${discount.toString().smartFormat()}',
          ),
        );
      }
    }

    return details;
  }

  @override
  Widget build(BuildContext context) {
    final paymentData = paymentDataModel;
    return ChangeNotifierProvider(
      create: (context) => PaymentProvider(paymentData),
      builder: (context, child) {
        final paymentProvider = context.watch<PaymentProvider>();
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.payments),
          backgroundColor: AppColors.ffF8F9FA,
          bottomNavigationBar: AppButton(
            onPressed: () => paymentProvider.navigateToWebViewForPayment(
              context,
              bookingData: bookingProviderData,
              bookingId: bookingId,
              isExclusiveTrip: isExclusiveTrip,
            ),
            text: '${paymentProvider.netTotalAmount}'.smartFormat(),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: AppSize.appPadding,
            ).add(EdgeInsets.only(bottom: AppSize.h16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: AppSize.h8,
              children: [
                ...paymentData?.paymentSummary?.map(
                      (e) => Padding(
                        padding: EdgeInsets.only(bottom: AppSize.h16),
                        child: CompanyPaymentSection(
                          companyName: e.companyName ?? 'Transport Company',
                          netTripCharge: e.netTripCharge
                              ?.toString()
                              .smartFormat(),
                          children: [
                            // Transportation Cost Breakdown
                            if (e.transportationCharge.isNotNullNotZero ||
                                e.netPayTransportationCharge.isNotNullNotZero)
                              DetailedPaymentBreakdown(
                                title: context.l10n.transportationCost,
                                explanation:
                                    context.l10n.transportationCostExplanation,
                                details: _getTransportationDetails(context, e),
                                grossAmount:
                                    e.transportationCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                                netAmount:
                                    e.netPayTransportationCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                              ),

                            // Start Location Storage Fee Breakdown
                            if (e
                                    .startStopLocationStorageCharge
                                    .isNotNullNotZero ||
                                e
                                    .netStartStopLocationStorageCharge
                                    .isNotNullNotZero)
                              DetailedPaymentBreakdown(
                                title: context.l10n.dropOffStorageFee,
                                explanation:
                                    context.l10n.storageCostExplanation,
                                details: _getStorageDetails(
                                  context,
                                  e.startStopLocationStorageCharge,
                                  e.netStartStopLocationStorageCharge,
                                ),
                                grossAmount:
                                    e.startStopLocationStorageCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                                netAmount:
                                    e.netStartStopLocationStorageCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                              ),

                            // End Location Storage Fee Breakdown
                            if (e
                                    .endStopLocationStorageCharge
                                    .isNotNullNotZero ||
                                e
                                    .netEndStopLocationStorageCharge
                                    .isNotNullNotZero)
                              DetailedPaymentBreakdown(
                                title: context.l10n.pickupStorageFee,
                                explanation:
                                    context.l10n.storageCostExplanation,
                                details: _getStorageDetails(
                                  context,
                                  e.endStopLocationStorageCharge,
                                  e.netEndStopLocationStorageCharge,
                                ),
                                grossAmount:
                                    e.endStopLocationStorageCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                                netAmount:
                                    e.netEndStopLocationStorageCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                              ),

                            // Customer Location to Start Location Service Fee Breakdown
                            if (e
                                    .customerLocationToStartLocationServiceCharge
                                    .isNotNullNotZero ||
                                e
                                    .netCustomerLocationToStartLocationServiceCharge
                                    .isNotNullNotZero)
                              DetailedPaymentBreakdown(
                                title: context.l10n.dropTransportation,
                                explanation:
                                    context.l10n.dropOffServiceExplanation,
                                grossAmount:
                                    e.customerLocationToStartLocationServiceCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                                netAmount:
                                    e.netCustomerLocationToStartLocationServiceCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                              ),

                            // Insurance Cost Breakdown
                            if (e.insuranceCharge.isNotNullNotZero ||
                                e.netInsuranceCharge.isNotNullNotZero)
                              DetailedPaymentBreakdown(
                                title: context.l10n.insuranceCost,
                                explanation:
                                    context.l10n.insuranceCostExplanation,
                                details: _getInsuranceDetails(
                                  context,
                                  e.insuranceCharge,
                                  e.netInsuranceCharge,
                                ),
                                grossAmount:
                                    e.insuranceCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                                netAmount:
                                    e.netInsuranceCharge
                                        ?.toString()
                                        .smartFormat() ??
                                    '',
                              ),

                            // Show exclusive trip note if applicable
                            if (e
                                .extraTransportationChargeForCustomer
                                .isNotNullNotZero)
                              Padding(
                                padding: EdgeInsets.only(top: AppSize.h12),
                                child: Container(
                                  padding: EdgeInsets.all(AppSize.h8),
                                  decoration: BoxDecoration(
                                    color: AppColors.red.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(
                                      AppSize.r4,
                                    ),
                                    border: Border.all(color: AppColors.red),
                                  ),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        size: AppSize.sp16,
                                        color: AppColors.red,
                                      ),
                                      SizedBox(width: AppSize.w8),
                                      Expanded(
                                        child: Text(
                                          context
                                              .l10n
                                              .exclusiveTripExtraCostNote,
                                          style: TextStyle(
                                            fontSize: AppSize.sp12,
                                            fontWeight: FontWeight.w500,
                                            color: AppColors.red,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ) ??
                    [],
                DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppSize.r8),
                    color: Colors.white,
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(AppSize.h16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.l10n.summary,
                          style: context.textTheme.titleLarge,
                        ),
                        Gap(AppSize.h8),

                        // Total Transportation Cost Breakdown
                        if ((paymentData
                                    ?.totalTransportationCharge
                                    .isNotNullNotZero ??
                                false) ||
                            (paymentData
                                    ?.totalNetTransportationCharge
                                    .isNotNullNotZero ??
                                false))
                          DetailedPaymentBreakdown(
                            title: context.l10n.transportationCost,
                            grossAmount:
                                paymentData?.totalTransportationCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                            netAmount:
                                paymentData?.totalNetTransportationCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),

                        // Total Start Location Storage Fee Breakdown
                        if (paymentData
                                ?.totalStartLocationStorageCharge
                                .isNotNullNotZero ??
                            false)
                          SummaryRow(
                            title: context.l10n.dropOffStorageFee,
                            amount:
                                paymentData?.totalStartLocationStorageCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),

                        // Total End Location Storage Fee Breakdown
                        if (paymentData
                                ?.totalEndLocationStorageCharge
                                .isNotNullNotZero ??
                            false)
                          SummaryRow(
                            title: context.l10n.pickupStorageFee,
                            amount:
                                paymentData?.totalEndLocationStorageCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),

                        // Total Insurance Cost Breakdown
                        if (paymentData
                                ?.totalInsuranceCharge
                                .isNotNullNotZero ??
                            false)
                          SummaryRow(
                            title: context.l10n.totalInsuranceCost,
                            amount:
                                paymentData?.totalInsuranceCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),

                        // App Fee Breakdown
                        if ((paymentData?.totalAppFee.isNotNullNotZero ??
                                false) ||
                            (paymentData?.netTotalAppFee.isNotNullNotZero ??
                                false))
                          DetailedPaymentBreakdown(
                            title: context.l10n.serviceFee,
                            grossAmount:
                                paymentData?.totalAppFee
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                            netAmount:
                                paymentData?.netTotalAppFee
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),

                        // Customer Location to Start Stop Location Service Charge
                        if (paymentData
                                ?.totalCustomerLocationToStartStopLocationServiceCharge
                                .isNotNullNotZero ??
                            false)
                          SummaryRow(
                            title: context.l10n.dropTransportation,
                            amount:
                                paymentData
                                    ?.totalCustomerLocationToStartStopLocationServiceCharge
                                    ?.toString()
                                    .smartFormat() ??
                                '',
                          ),

                        // Final Payment Summary
                        if (paymentData?.totalNetBookingPay.isNotNullNotZero ??
                            false)
                          Padding(
                            padding: EdgeInsets.only(top: AppSize.h20),
                            child: Container(
                              padding: EdgeInsets.all(AppSize.h16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppColors.primaryColor.withValues(
                                      alpha: 0.1,
                                    ),
                                    AppColors.primaryColor.withValues(
                                      alpha: 0.05,
                                    ),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(
                                  AppSize.r12,
                                ),
                                border: Border.all(
                                  color: AppColors.primaryColor,
                                  width: 2,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.payment,
                                        color: AppColors.primaryColor,
                                        size: AppSize.sp20,
                                      ),
                                      SizedBox(width: AppSize.w8),
                                      Text(
                                        context.l10n.finalAmountToPay,
                                        style: context.textTheme.titleMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: AppColors.primaryColor,
                                            ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: AppSize.h8),
                                  Text(
                                    context.l10n.youPayNow,
                                    style: context.textTheme.bodySmall
                                        ?.copyWith(
                                          color: AppColors.ff6C757D,
                                          fontSize: AppSize.sp12,
                                        ),
                                  ),
                                  SizedBox(height: AppSize.h12),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Flexible(
                                        child: Text(
                                          'Amount to Pay Now:',
                                          style: context.textTheme.titleLarge
                                              ?.copyWith(
                                                fontWeight: FontWeight.w700,
                                                color: AppColors.primaryColor,
                                              ),
                                        ),
                                      ),
                                      Flexible(
                                        child: Text(
                                          paymentData?.totalNetBookingPay
                                                  ?.toString()
                                                  .smartFormat() ??
                                              '',
                                          style: context.textTheme.titleLarge
                                              ?.copyWith(
                                                fontWeight: FontWeight.w700,
                                                color: AppColors.primaryColor,
                                                fontSize: AppSize.sp20,
                                              ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(AppSize.h16),
                  margin: EdgeInsets.only(top: AppSize.h8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppSize.r8),
                    color: Colors.white,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.l10n.total,
                        style: context.textTheme.titleLarge,
                      ),
                      Flexible(
                        child: MarqueeWidget(
                          child: Text(
                            '${paymentProvider.totalAmount}'.smartFormat(),
                            style: context.textTheme.titleLarge,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Builder(
                  builder: (context) {
                    final pay =
                        (paymentProvider.netTotalAmount /
                            paymentProvider.totalAmount) *
                        100;
                    final remain = 100 - pay;
                    return Text(
                      context.l10n
                          .noteUHaveToPay(
                            pay.toStringAsFixed(1),
                            remain.toStringAsFixed(1),
                          )
                          .smartFormat(),
                      style: context.textTheme.bodySmall?.copyWith(
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.w400,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
