import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string.dart';
import 'package:transport_match/extensions/num_extension.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/booking_module/pages/payment_page/provider/payment_provider.dart';

import 'package:transport_match/presentation/modules/home_module/pages/models/payment_summary_model.dart';
import 'package:transport_match/presentation/modules/home_module/pages/models/req_model.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/widgets/app_button.dart';
import 'package:transport_match/widgets/custom_app_bar.dart';
import 'package:transport_match/widgets/marqee_widget.dart';

/// Payment page
class PaymentScreen extends StatelessWidget {
  /// Constructor
  const PaymentScreen({
    super.key,
    required this.isExclusiveTrip,
    required this.bookingId,
    required this.paymentDataModel,
    required this.bookingProviderData,
  }) : assert(
         bookingProviderData != null || paymentDataModel != null,
         'Please pass payment and booking data',
       );
  final bool isExclusiveTrip;
  final String? bookingId;
  final PaymentDataModel? paymentDataModel;
  final ProviderData? bookingProviderData;

  /// Build provider section with expansion tile
  Widget _buildProviderSection(
    BuildContext context,
    PaymentSummaryModel provider,
  ) {
    return Card(
      margin: EdgeInsets.only(bottom: AppSize.h12),
      child: ExpansionTile(
        title: Row(
          children: [
            const Icon(Icons.local_shipping, color: AppColors.primaryColor),
            SizedBox(width: AppSize.w8),
            Expanded(
              child: Text(
                provider.companyName ?? 'Transport Company',
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            Text(
              provider.totalTripCost?.toString().smartFormat() ?? '',
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primaryColor,
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(AppSize.h16),
            child: Column(
              children: [
                // Transportation Cost
                if (provider.transportationCharge.isNotNullNotZero)
                  _buildCostRow(
                    context.l10n.transportationCost,
                    provider.transportationCharge!.toString().smartFormat(),
                  ),

                // Storage Fees
                if (provider.startStopLocationStorageCharge.isNotNullNotZero)
                  _buildCostRow(
                    context.l10n.dropOffStorageFee,
                    provider.startStopLocationStorageCharge!
                        .toString()
                        .smartFormat(),
                  ),

                if (provider.endStopLocationStorageCharge.isNotNullNotZero)
                  _buildCostRow(
                    context.l10n.pickupStorageFee,
                    provider.endStopLocationStorageCharge!
                        .toString()
                        .smartFormat(),
                  ),

                // Customer Location Service
                if (provider
                    .customerLocationToStartLocationServiceCharge
                    .isNotNullNotZero)
                  _buildCostRow(
                    context.l10n.dropTransportation,
                    provider.customerLocationToStartLocationServiceCharge!
                        .toString()
                        .smartFormat(),
                  ),

                // Insurance
                if (provider.insuranceCharge.isNotNullNotZero)
                  _buildCostRow(
                    context.l10n.insuranceCost,
                    provider.insuranceCharge!.toString().smartFormat(),
                  ),

                // Car charges expansion tile
                if (provider.carPaymentData?.carCharges?.isNotEmpty ?? false)
                  ExpansionTile(
                    title: const Text('View Car Charges'),
                    children: provider.carPaymentData!.carCharges!.entries
                        .map(
                          (entry) => _buildCarChargeSection(
                            context,
                            entry.key,
                            entry.value,
                          ),
                        )
                        .toList(),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build car charge section
  Widget _buildCarChargeSection(
    BuildContext context,
    String serialNumber,
    CarChargeData carData,
  ) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: AppSize.h4),
      child: Padding(
        padding: EdgeInsets.all(AppSize.h12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Car Charges for $serialNumber',
              style: context.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppSize.h8),

            if (carData.transportationChargeForCar.isNotNullNotZero)
              _buildCostRow(
                context.l10n.transportationCost,
                carData.transportationChargeForCar!.toString().smartFormat(),
                isSubItem: true,
              ),

            if (carData.startStopLocationChargeForCar.isNotNullNotZero)
              _buildCostRow(
                context.l10n.dropOffStorageFee,
                carData.startStopLocationChargeForCar!.toString().smartFormat(),
                isSubItem: true,
              ),

            if (carData.endStopLocationChargeForCar.isNotNullNotZero)
              _buildCostRow(
                context.l10n.pickupStorageFee,
                carData.endStopLocationChargeForCar!.toString().smartFormat(),
                isSubItem: true,
              ),

            if (carData.insuranceChargeForCar.isNotNullNotZero)
              _buildCostRow(
                context.l10n.insuranceCost,
                carData.insuranceChargeForCar!.toString().smartFormat(),
                isSubItem: true,
              ),
          ],
        ),
      ),
    );
  }

  /// Build cost row
  Widget _buildCostRow(String title, String amount, {bool isSubItem = false}) {
    return Padding(
      padding: EdgeInsets.only(
        top: AppSize.h4,
        left: isSubItem ? AppSize.w16 : 0,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: isSubItem ? AppSize.sp13 : AppSize.sp14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isSubItem ? AppSize.sp13 : AppSize.sp14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build summary row with optional net amount
  Widget _buildSummaryRow(String title, String amount, {String? netAmount}) {
    return Padding(
      padding: EdgeInsets.only(top: AppSize.h8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: AppSize.sp14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                amount,
                style: TextStyle(
                  fontSize: AppSize.sp14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          if (netAmount != null && netAmount.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: AppSize.h4, left: AppSize.w16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '• Net Amount (You Pay)',
                    style: TextStyle(
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w500,
                      color: AppColors.primaryColor,
                    ),
                  ),
                  Text(
                    netAmount,
                    style: TextStyle(
                      fontSize: AppSize.sp12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final paymentData = paymentDataModel;
    return ChangeNotifierProvider(
      create: (context) => PaymentProvider(paymentData),
      builder: (context, child) {
        final paymentProvider = context.watch<PaymentProvider>();
        return Scaffold(
          appBar: CustomAppBar(title: context.l10n.payments),
          backgroundColor: AppColors.ffF8F9FA,
          bottomNavigationBar: AppButton(
            onPressed: () => paymentProvider.navigateToWebViewForPayment(
              context,
              bookingData: bookingProviderData,
              bookingId: bookingId,
              isExclusiveTrip: isExclusiveTrip,
            ),
            text: '${paymentProvider.netTotalAmount}'.smartFormat(),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(
              horizontal: AppSize.appPadding,
            ).add(EdgeInsets.only(bottom: AppSize.h16)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: AppSize.h8,
              children: [
                // Provider Breakdown Section
                if (paymentData?.paymentSummary?.isNotEmpty ?? false)
                  ExpansionTile(
                    initiallyExpanded: true,
                    title: Text(
                      'Provider Breakdown',
                      style: context.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    children: paymentData!.paymentSummary!
                        .map(
                          (provider) =>
                              _buildProviderSection(context, provider),
                        )
                        .toList(),
                  ),

                DecoratedBox(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppSize.r8),
                    color: Colors.white,
                  ),
                  child: Padding(
                    padding: EdgeInsets.all(AppSize.h16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Overall Summary Section
                        ExpansionTile(
                          initiallyExpanded: true,
                          title: Text(
                            'Overall Summary',
                            style: context.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          children: [
                            Padding(
                              padding: EdgeInsets.all(AppSize.h16),
                              child: Column(
                                children: [
                                  // Transportation Cost with both total and net
                                  if (paymentData
                                          ?.totalTransportationCharge
                                          .isNotNullNotZero ??
                                      false)
                                    _buildSummaryRow(
                                      'Transportation Cost',
                                      paymentData!.totalTransportationCharge!
                                          .toString()
                                          .smartFormat(),
                                      netAmount: paymentData
                                          .totalNetTransportationCharge
                                          ?.toString()
                                          .smartFormat(),
                                    ),

                                  // Storage Charges
                                  if (paymentData
                                          ?.totalStartLocationStorageCharge
                                          .isNotNullNotZero ??
                                      false)
                                    _buildSummaryRow(
                                      'Start Location Storage',
                                      paymentData!
                                          .totalStartLocationStorageCharge!
                                          .toString()
                                          .smartFormat(),
                                    ),

                                  if (paymentData
                                          ?.totalEndLocationStorageCharge
                                          .isNotNullNotZero ??
                                      false)
                                    _buildSummaryRow(
                                      'End Location Storage',
                                      paymentData!
                                          .totalEndLocationStorageCharge!
                                          .toString()
                                          .smartFormat(),
                                    ),

                                  // Insurance with both total and net
                                  if (paymentData
                                          ?.totalInsuranceCharge
                                          .isNotNullNotZero ??
                                      false)
                                    _buildSummaryRow(
                                      'Insurance Cost',
                                      paymentData!.totalInsuranceCharge!
                                          .toString()
                                          .smartFormat(),
                                      netAmount: paymentData
                                          .totalNetInsuranceCharge
                                          ?.toString()
                                          .smartFormat(),
                                    ),

                                  // App Fee with both total and net
                                  if (paymentData
                                          ?.totalAppFee
                                          .isNotNullNotZero ??
                                      false)
                                    _buildSummaryRow(
                                      'Platform Fee',
                                      paymentData!.totalAppFee!
                                          .toString()
                                          .smartFormat(),
                                      netAmount: paymentData.netTotalAppFee
                                          ?.toString()
                                          .smartFormat(),
                                    ),

                                  // Customer Location Service
                                  if (paymentData
                                          ?.totalCustomerLocationToStartStopLocationServiceCharge
                                          .isNotNullNotZero ??
                                      false)
                                    _buildSummaryRow(
                                      'Customer Location Service',
                                      paymentData!
                                          .totalCustomerLocationToStartStopLocationServiceCharge!
                                          .toString()
                                          .smartFormat(),
                                    ),

                                  // Tax Information
                                  if (paymentData?.taxAmount.isNotNullNotZero ??
                                      false)
                                    Column(
                                      children: [
                                        Divider(height: AppSize.h24),
                                        _buildSummaryRow(
                                          'Tax (${paymentData!.taxRate?.toString() ?? '0'}%)',
                                          paymentData.taxAmount!
                                              .toString()
                                              .smartFormat(),
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),

                        // Final Payment Summary
                        if (paymentData?.netTotalWithTax.isNotNullNotZero ??
                            false)
                          Padding(
                            padding: EdgeInsets.only(top: AppSize.h20),
                            child: Container(
                              padding: EdgeInsets.all(AppSize.h16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    AppColors.primaryColor.withValues(
                                      alpha: 0.1,
                                    ),
                                    AppColors.primaryColor.withValues(
                                      alpha: 0.05,
                                    ),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(
                                  AppSize.r12,
                                ),
                                border: Border.all(
                                  color: AppColors.primaryColor,
                                  width: 2,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      const Icon(
                                        Icons.payment,
                                        color: AppColors.primaryColor,
                                        size: 20,
                                      ),
                                      SizedBox(width: AppSize.w8),
                                      Text(
                                        'Final Amount to Pay',
                                        style: context.textTheme.titleMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: AppColors.primaryColor,
                                            ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(height: AppSize.h8),
                                  Text(
                                    'Pay now to confirm your booking',
                                    style: context.textTheme.bodySmall
                                        ?.copyWith(
                                          color: AppColors.ff6C757D,
                                          fontSize: AppSize.sp12,
                                        ),
                                  ),
                                  SizedBox(height: AppSize.h12),

                                  // Show breakdown of final amount
                                  if (paymentData
                                          ?.totalNetBookingPay
                                          .isNotNullNotZero ??
                                      false)
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          'Net Amount:',
                                          style: context.textTheme.bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w500,
                                              ),
                                        ),
                                        Text(
                                          paymentData!.totalNetBookingPay!
                                              .toString()
                                              .smartFormat(),
                                          style: context.textTheme.bodyMedium
                                              ?.copyWith(
                                                fontWeight: FontWeight.w600,
                                              ),
                                        ),
                                      ],
                                    ),

                                  if (paymentData?.taxAmount.isNotNullNotZero ??
                                      false)
                                    Padding(
                                      padding: EdgeInsets.only(top: AppSize.h4),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            'Tax:',
                                            style: context.textTheme.bodyMedium
                                                ?.copyWith(
                                                  fontWeight: FontWeight.w500,
                                                ),
                                          ),
                                          Text(
                                            paymentData!.taxAmount!
                                                .toString()
                                                .smartFormat(),
                                            style: context.textTheme.bodyMedium
                                                ?.copyWith(
                                                  fontWeight: FontWeight.w600,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),

                                  Divider(height: AppSize.h16),

                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Total Amount to Pay:',
                                        style: context.textTheme.titleLarge
                                            ?.copyWith(
                                              fontWeight: FontWeight.w700,
                                              color: AppColors.primaryColor,
                                            ),
                                      ),
                                      Text(
                                        paymentData!.netTotalWithTax!
                                            .toString()
                                            .smartFormat(),
                                        style: context.textTheme.titleLarge
                                            ?.copyWith(
                                              fontWeight: FontWeight.w700,
                                              color: AppColors.primaryColor,
                                              fontSize: AppSize.sp20,
                                            ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(AppSize.h16),
                  margin: EdgeInsets.only(top: AppSize.h8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(AppSize.r8),
                    color: Colors.white,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        context.l10n.total,
                        style: context.textTheme.titleLarge,
                      ),
                      Flexible(
                        child: MarqueeWidget(
                          child: Text(
                            '${paymentProvider.totalAmount}'.smartFormat(),
                            style: context.textTheme.titleLarge,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Builder(
                  builder: (context) {
                    final pay =
                        (paymentProvider.netTotalAmount /
                            paymentProvider.totalAmount) *
                        100;
                    final remain = 100 - pay;
                    return Text(
                      context.l10n
                          .noteUHaveToPay(
                            pay.toStringAsFixed(1),
                            remain.toStringAsFixed(1),
                          )
                          .smartFormat(),
                      style: context.textTheme.bodySmall?.copyWith(
                        color: AppColors.ff6C757D,
                        fontWeight: FontWeight.w400,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
