class PaymentDataModel {
  const PaymentDataModel({
    this.paymentSummary,
    this.totalStartLocationStorageCharge,
    this.totalAppFee,
    this.netTotalAppFee,
    this.totalCustomerLocationToStartStopLocationServiceCharge,
    this.totalTransportationCharge,
    this.totalNetTransportationCharge,
    this.totalEndLocationStorageCharge,
    this.totalNetBookingPay,
    this.totalBookingCost,
    this.totalInsuranceCharge,
    this.totalNetInsuranceCharge,
    this.taxAmount,
    this.taxRate,
    this.totalBookingCostWithTax,
    this.netTotalWithTax,
  });

  factory PaymentDataModel.fromJson(
    Map<String, dynamic> json,
  ) => PaymentDataModel(
    paymentSummary: json['payment_summary'] == null
        ? []
        : List<PaymentSummaryModel>.from(
            (json['payment_summary'] as List?)?.map(
                  (x) =>
                      PaymentSummaryModel.fromJson(x as Map<String, dynamic>),
                ) ??
                [],
          ),
    totalStartLocationStorageCharge:
        json['total_start_location_storage_charge'] as num?,
    totalAppFee: json['total_app_fee'] as num?,
    netTotalAppFee: json['net_total_app_fee'] as num?,
    totalCustomerLocationToStartStopLocationServiceCharge:
        json['total_customer_location_to_start_stop_location_service_charge']
            as num?,
    totalTransportationCharge: json['total_transportation_charge'] as num?,
    totalNetTransportationCharge:
        json['total_net_transportation_charge'] as num?,
    totalEndLocationStorageCharge:
        json['total_end_location_storage_charge'] as num?,
    totalNetBookingPay: json['total_net_booking_pay'] as num?,
    totalBookingCost: json['total_booking_cost'] as num?,
    totalInsuranceCharge: json['total_insurance_charge'] as num?,
    totalNetInsuranceCharge: json['total_net_insurance_charge'] as num?,
    taxAmount: json['tax_amount'] as num?,
    taxRate: json['tax_rate'] as num?,
    totalBookingCostWithTax: json['total_booking_cost_with_tax'] as num?,
    netTotalWithTax: json['net_total_with_tax'] as num?,
  );
  final List<PaymentSummaryModel>? paymentSummary;
  final num? totalStartLocationStorageCharge;
  final num? totalAppFee;
  final num? netTotalAppFee;
  final num? totalCustomerLocationToStartStopLocationServiceCharge;
  final num? totalTransportationCharge;
  final num? totalNetTransportationCharge;
  final num? totalEndLocationStorageCharge;
  final num? totalNetBookingPay;
  final num? totalBookingCost;
  final num? totalInsuranceCharge;
  final num? totalNetInsuranceCharge;
  final num? taxAmount;
  final num? taxRate;
  final num? totalBookingCostWithTax;
  final num? netTotalWithTax;

  Map<String, dynamic> toJson() => {
    'payment_summary': paymentSummary == null
        ? []
        : List<dynamic>.from(paymentSummary!.map((x) => x.toJson())),
    'total_start_location_storage_charge': totalStartLocationStorageCharge,
    'total_app_fee': totalAppFee,
    'net_total_app_fee': netTotalAppFee,
    'total_customer_location_to_start_stop_location_service_charge':
        totalCustomerLocationToStartStopLocationServiceCharge,
    'total_transportation_charge': totalTransportationCharge,
    'total_insurance_charge': totalInsuranceCharge,
    'total_net_transportation_charge': totalNetTransportationCharge,
    'total_end_location_storage_charge': totalEndLocationStorageCharge,
    'total_net_booking_pay': totalNetBookingPay,
    'total_booking_cost': totalBookingCost,
    'total_net_insurance_charge': totalNetInsuranceCharge,
    'tax_amount': taxAmount,
    'tax_rate': taxRate,
    'total_booking_cost_with_tax': totalBookingCostWithTax,
    'net_total_with_tax': netTotalWithTax,
  };
}

class PaymentSummaryModel {
  const PaymentSummaryModel({
    this.tripId,
    this.companyName,
    this.transportationCharge,
    this.startStopLocationStorageCharge,
    this.endStopLocationStorageCharge,
    this.customerLocationToStartLocationServiceCharge,
    this.netPayTransportationCharge,
    this.netStartStopLocationStorageCharge,
    this.netEndStopLocationStorageCharge,
    this.netCustomerLocationToStartLocationServiceCharge,
    this.netTripCharge,
    this.totalTripCost,
    this.actualTransportationChargeForCustomer,
    this.extraTransportationChargeForCustomer,
    this.insuranceCharge,
    this.actualTripCost,
    this.netInsuranceCharge,
    this.customerRequiredSlotSize,
    this.emptySlots,
    this.carPaymentData,
  });

  factory PaymentSummaryModel.fromJson(
    Map<String, dynamic> json,
  ) => PaymentSummaryModel(
    tripId: json['trip_id'] as int?,
    companyName: json['company_name'] as String?,
    transportationCharge: json['transportation_charge'] as num?,
    startStopLocationStorageCharge:
        json['start_stop_location_storage_charge'] as num?,
    endStopLocationStorageCharge:
        json['end_stop_location_storage_charge'] as num?,
    customerLocationToStartLocationServiceCharge:
        json['customer_location_to_start_location_service_charge'] as num?,
    netPayTransportationCharge: json['net_pay_transportation_charge'] as num?,
    netStartStopLocationStorageCharge:
        json['net_start_stop_location_storage_charge'] as num?,
    netEndStopLocationStorageCharge:
        json['net_end_stop_location_storage_charge'] as num?,
    netCustomerLocationToStartLocationServiceCharge:
        json['net_customer_location_to_start_location_service_charge'] as num?,
    netTripCharge: json['net_trip_charge'] as num?,
    totalTripCost: json['total_trip_cost'] as num?,
    actualTransportationChargeForCustomer:
        json['actual_transportation_charge_for_customer'] as num?,
    extraTransportationChargeForCustomer:
        json['extra_transportation_charge_for_customer'] as num?,
    insuranceCharge: json['insurance_charge'] as num?,
    actualTripCost: json['actual_trip_cost'] as num?,
    netInsuranceCharge: json['net_insurance_charge'] as num?,
    customerRequiredSlotSize: json['customer_required_slot_size'] as num?,
    emptySlots: json['empty_slots'] as num?,
    carPaymentData: json['car_payment_data'] != null
        ? CarPaymentData.fromJson(
            json['car_payment_data'] as Map<String, dynamic>,
          )
        : null,
  );
  final int? tripId;
  final String? companyName;
  final num? transportationCharge;
  final num? startStopLocationStorageCharge;
  final num? endStopLocationStorageCharge;
  final num? customerLocationToStartLocationServiceCharge;
  final num? netPayTransportationCharge;
  final num? netStartStopLocationStorageCharge;
  final num? netEndStopLocationStorageCharge;
  final num? netCustomerLocationToStartLocationServiceCharge;
  final num? netTripCharge;
  final num? totalTripCost;
  final num? actualTransportationChargeForCustomer;
  final num? extraTransportationChargeForCustomer;
  final num? insuranceCharge;
  final num? actualTripCost;
  final num? netInsuranceCharge;
  final num? customerRequiredSlotSize;
  final num? emptySlots;
  final CarPaymentData? carPaymentData;

  Map<String, dynamic> toJson() => {
    'trip_id': tripId,
    'company_name': companyName,
    'transportation_charge': transportationCharge,
    'actual_transportation_charge_for_customer':
        actualTransportationChargeForCustomer,
    'extra_transportation_charge_for_customer':
        extraTransportationChargeForCustomer,
    'net_pay_transportation_charge': netPayTransportationCharge,
    'insurance_charge': insuranceCharge,
    'actual_trip_cost': actualTripCost,
    'net_insurance_charge': netInsuranceCharge,
    'net_trip_charge': netTripCharge,
    'total_trip_cost': totalTripCost,
    'customer_required_slot_size': customerRequiredSlotSize,
    'empty_slots': emptySlots,
    'start_stop_location_storage_charge': startStopLocationStorageCharge,
    'end_stop_location_storage_charge': endStopLocationStorageCharge,
    'customer_location_to_start_location_service_charge':
        customerLocationToStartLocationServiceCharge,
    'net_start_stop_location_storage_charge': netStartStopLocationStorageCharge,
    'net_end_stop_location_storage_charge': netEndStopLocationStorageCharge,
    'net_customer_location_to_start_location_service_charge':
        netCustomerLocationToStartLocationServiceCharge,
    'car_payment_data': carPaymentData?.toJson(),
  };
}

class CarPaymentData {
  const CarPaymentData({
    this.stopLocationStorageCharge,
    this.endLocationStorageCharge,
    this.carCharges,
  });

  factory CarPaymentData.fromJson(Map<String, dynamic> json) {
    final carCharges = <String, CarChargeData>{};

    // Parse car-specific charges (excluding the general charges)
    json.forEach((key, value) {
      if (key != 'stop_location_storage_charge' &&
          key != 'end_location_storage_charge' &&
          value is Map<String, dynamic>) {
        carCharges[key] = CarChargeData.fromJson(value);
      }
    });

    return CarPaymentData(
      stopLocationStorageCharge: json['stop_location_storage_charge'] as num?,
      endLocationStorageCharge: json['end_location_storage_charge'] as num?,
      carCharges: carCharges,
    );
  }

  final num? stopLocationStorageCharge;
  final num? endLocationStorageCharge;
  final Map<String, CarChargeData>? carCharges;

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'stop_location_storage_charge': stopLocationStorageCharge,
      'end_location_storage_charge': endLocationStorageCharge,
    };

    carCharges?.forEach((key, value) {
      json[key] = value.toJson();
    });

    return json;
  }
}

class CarChargeData {
  const CarChargeData({
    this.serialNumber,
    this.startStopLocationChargeForCar,
    this.netStartStopLocationChargeForCar,
    this.carStaysAtStartStopLocation,
    this.endStopLocationChargeForCar,
    this.netEndStopLocationChargeForCar,
    this.transportationChargeForCar,
    this.netTransportationChargeForCar,
    this.insuranceChargeForCar,
    this.netInsuranceChargeForCar,
    this.customerLocationToStartLocationServiceChargeForCar,
    this.netCustomerLocationToStartLocationServiceChargeForCar,
  });

  factory CarChargeData.fromJson(Map<String, dynamic> json) => CarChargeData(
    serialNumber: json['serial_number'] as String?,
    startStopLocationChargeForCar:
        json['start_stop_location_charge_for_car'] as num?,
    netStartStopLocationChargeForCar:
        json['net_start_stop_location_charge_for_car'] as num?,
    carStaysAtStartStopLocation:
        json['car_stays_at_start_stop_location'] as num?,
    endStopLocationChargeForCar:
        json['end_stop_location_charge_for_car'] as num?,
    netEndStopLocationChargeForCar:
        json['net_end_stop_location_charge_for_car'] as num?,
    transportationChargeForCar: json['transportation_charge_for_car'] as num?,
    netTransportationChargeForCar:
        json['net_transportation_charge_for_car'] as num?,
    insuranceChargeForCar: json['insurance_charge_for_car'] as num?,
    netInsuranceChargeForCar: json['net_insurance_charge_for_car'] as num?,
    customerLocationToStartLocationServiceChargeForCar:
        json['customer_location_to_start_location_service_charge_for_car']
            as num?,
    netCustomerLocationToStartLocationServiceChargeForCar:
        json['net_customer_location_to_start_location_service_charge_for_car']
            as num?,
  );

  final String? serialNumber;
  final num? startStopLocationChargeForCar;
  final num? netStartStopLocationChargeForCar;
  final num? carStaysAtStartStopLocation;
  final num? endStopLocationChargeForCar;
  final num? netEndStopLocationChargeForCar;
  final num? transportationChargeForCar;
  final num? netTransportationChargeForCar;
  final num? insuranceChargeForCar;
  final num? netInsuranceChargeForCar;
  final num? customerLocationToStartLocationServiceChargeForCar;
  final num? netCustomerLocationToStartLocationServiceChargeForCar;

  Map<String, dynamic> toJson() => {
    'serial_number': serialNumber,
    'start_stop_location_charge_for_car': startStopLocationChargeForCar,
    'net_start_stop_location_charge_for_car': netStartStopLocationChargeForCar,
    'car_stays_at_start_stop_location': carStaysAtStartStopLocation,
    'end_stop_location_charge_for_car': endStopLocationChargeForCar,
    'net_end_stop_location_charge_for_car': netEndStopLocationChargeForCar,
    'transportation_charge_for_car': transportationChargeForCar,
    'net_transportation_charge_for_car': netTransportationChargeForCar,
    'insurance_charge_for_car': insuranceChargeForCar,
    'net_insurance_charge_for_car': netInsuranceChargeForCar,
    'customer_location_to_start_location_service_charge_for_car':
        customerLocationToStartLocationServiceChargeForCar,
    'net_customer_location_to_start_location_service_charge_for_car':
        netCustomerLocationToStartLocationServiceChargeForCar,
  };
}
