import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/extensions/ext_string_alert.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/presentation/modules/home_module/provider/home_provider.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_dropdown.dart';

class StopEndLocationWidget extends StatefulWidget {
  const StopEndLocationWidget({super.key});

  @override
  State<StopEndLocationWidget> createState() => _StopEndLocationWidgetState();
}

class _StopEndLocationWidgetState extends State<StopEndLocationWidget> {
  final SingleValueDropDownController selectedOriginStockLocationController =
      SingleValueDropDownController();
  final SingleValueDropDownController selectedDropStockLocationController =
      SingleValueDropDownController();

  @override
  void dispose() {
    selectedOriginStockLocationController.dispose();
    selectedDropStockLocationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppSize.h16),
      child: Consumer<HomeProvider>(
        builder: (context, homeProvider, child) {
          return Container(
            padding: EdgeInsets.all(AppSize.h16),
            margin: EdgeInsets.only(top: AppSize.h8),
            width: double.maxFinite,
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(AppSize.r8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: AppSize.h4,
              children: [
                Text(
                  context.l10n.stockLocations,
                  style: context.textTheme.titleLarge?.copyWith(
                    fontSize: AppSize.sp20,
                    color: AppColors.ff212529,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(top: AppSize.h12),
                  child: Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: AppSize.h4),
                        child: AppAssets.iconsLocationOrigin.image(
                          height: AppSize.h16,
                          width: AppSize.w14,
                        ),
                      ),
                      Text(
                        context.l10n.originStockLocation,
                        style: context.textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(bottom: AppSize.h16),
                  child: ValueListenableBuilder(
                    valueListenable: homeProvider.originStockLocationList,
                    builder: (context, value, child) {
                      final selectedItem =
                          homeProvider.selectedOriginStockLocation.value;

                      // Update controller when selectedItem changes
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        if (selectedItem != null) {
                          selectedOriginStockLocationController.setDropDown(
                            DropDownValueModel(
                              value: selectedItem.name?.toString() ?? '',
                              name: selectedItem.name?.toString() ?? '',
                            ),
                          );
                        } else {
                          selectedOriginStockLocationController.clearDropDown();
                        }
                      });

                      return GestureDetector(
                        onTap: () => context.l10n.pleaseSearchOriginStopLocation
                            .showErrorAlert(),
                        child: AbsorbPointer(
                          absorbing: value.isEmpty,
                          child: AppDropdown(
                            controller: selectedOriginStockLocationController,
                            items: value
                                .map(
                                  (e) => DropDownValueModel(
                                    name: e.name?.toString() ?? '',
                                    value: e.name?.toString() ?? '',
                                  ),
                                )
                                .toList(),
                            validator: (p0) => commonValidator(
                              errorMessage:
                                  context.l10n.chooseOriginStockLocation,
                              inputValue: p0,
                            ),
                            onChanged: (value) {
                              if (value is DropDownValueModel &&
                                  value.value != null) {
                                homeProvider
                                    .selectedOriginStockLocation
                                    .value = homeProvider
                                    .originStockLocationList
                                    .value
                                    .firstWhere((e) => e.name == value.value);
                              }
                            },
                            hintText: context.l10n.chooseOriginStockLocation,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: AppSize.h4),
                      child: AppAssets.iconsLocation.image(
                        height: AppSize.h16,
                        width: AppSize.w14,
                      ),
                    ),
                    Text(
                      context.l10n.dropStockLocation,
                      style: context.textTheme.bodyMedium,
                    ),
                  ],
                ),
                ValueListenableBuilder(
                  valueListenable: homeProvider.dropStockLocationList,
                  builder: (context, value, child) {
                    final selectedItem =
                        homeProvider.selectedDropStockLocation.value;

                    // Update controller when selectedItem changes
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      if (selectedItem != null) {
                        selectedDropStockLocationController.setDropDown(
                          DropDownValueModel(
                            value: selectedItem.name?.toString() ?? '',
                            name: selectedItem.name?.toString() ?? '',
                          ),
                        );
                      } else {
                        selectedDropStockLocationController.clearDropDown();
                      }
                    });

                    return GestureDetector(
                      onTap: () => context.l10n.pleaseSearchDropStopLocation
                          .showErrorAlert(),
                      child: AbsorbPointer(
                        absorbing: value.isEmpty,
                        child: AppDropdown(
                          controller: selectedDropStockLocationController,
                          items: value
                              .map(
                                (e) => DropDownValueModel(
                                  name: e.name?.toString() ?? '',
                                  value: e.name?.toString() ?? '',
                                ),
                              )
                              .toList(),
                          validator: (p0) => commonValidator(
                            errorMessage: context.l10n.chooseDropStockLocation,
                            inputValue: p0,
                          ),
                          onChanged: (value) {
                            if (value is DropDownValueModel &&
                                value.value != null) {
                              homeProvider.selectedDropStockLocation.value =
                                  homeProvider.dropStockLocationList.value
                                      .firstWhere((e) => e.name == value.value);
                            }
                          },
                          hintText: context.l10n.chooseDropStockLocation,
                        ),
                      ),
                    );
                    // AppDropdown<String>(
                    //   fillColor: AppColors.ffF8F9FA,
                    //   labelText: context.l10n.chooseDropStockLocation,
                    //   validator: (p0) => commonValidator(
                    //     inputValue: p0,
                    //     errorMessage: context.l10n.chooseDropStockLocation,
                    //   ),
                    //   items: value
                    //       .map(
                    //         (e) => DropdownMenuItem<String>(
                    //           value: e.name?.toString() ?? '',
                    //           child: Text(e.name?.toString() ?? ''),
                    //         ),
                    //       )
                    //       .toList(),
                    //   selectedItem: selectedItem?.name,
                    //   onChanged: (value) {
                    //     if (value != null) {
                    //       homeProvider.selectedDropStockLocation.value = homeProvider
                    //           .dropStockLocationList.value
                    //           .firstWhere((e) => e.name == value);
                    //     }
                    //     // Handle selection
                    //   },
                    // );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
