import 'dart:io' show File;

import 'package:dropdown_textfield/dropdown_textfield.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:image_picker/image_picker.dart';
import 'package:transport_match/extensions/ext_build_context.dart';
import 'package:transport_match/l10n/l10n.dart';
import 'package:transport_match/utils/app_colors.dart';
import 'package:transport_match/utils/app_size.dart';
import 'package:transport_match/utils/enums.dart';
import 'package:transport_match/utils/gen/assets.gen.dart';
import 'package:transport_match/utils/validators/global_text_validator.dart';
import 'package:transport_match/widgets/app_dropdown.dart';
import 'package:transport_match/widgets/app_image.dart';
import 'package:transport_match/widgets/app_textfield.dart';
import 'package:transport_match/widgets/widget_zoom/widget_zoom.dart';

class BuildPickupPersonInfoWidget extends StatefulWidget {
  const BuildPickupPersonInfoWidget({
    super.key,
    this.isEdit = false,
    required this.title,
    required this.subtitle,
    required this.image,
    required this.name,
    required this.docType,
    required this.onTypeChange,
    required this.onImageChange,
  });

  final bool isEdit;
  final String title;
  final String subtitle;
  final String image;
  final TextEditingController name;
  final String docType;
  final void Function(String? value) onTypeChange;
  final void Function(String? value) onImageChange;

  @override
  State<BuildPickupPersonInfoWidget> createState() =>
      _BuildPickupPersonInfoWidgetState();
}

class _BuildPickupPersonInfoWidgetState
    extends State<BuildPickupPersonInfoWidget> {
  final SingleValueDropDownController docTypeController =
      SingleValueDropDownController();

  @override
  void dispose() {
    docTypeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppSize.h12),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(AppSize.r8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(widget.title, style: context.textTheme.titleLarge),
          Gap(AppSize.h16),
          Text(widget.subtitle, style: context.textTheme.bodyMedium),
          Padding(
            padding: EdgeInsets.symmetric(vertical: AppSize.h16),
            child: AppTextFormField(
              hintText: context.l10n.enterPersonName,
              title: context.l10n.name,
              controller: widget.name,
              keyboardType: TextInputType.emailAddress,
              fillColor: AppColors.ffF8F9FA,
              titleColor: AppColors.black,
              readOnly: !widget.isEdit,
              validator: (p0) => nameValidator().call(p0),
            ),
          ),
          if (widget.isEdit)
            Builder(
              builder: (context) {
                // Update controller when docType changes
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (widget.docType.isNotEmpty) {
                    docTypeController.setDropDown(
                      DropDownValueModel(
                        value: widget.docType,
                        name: widget.docType,
                      ),
                    );
                  } else {
                    docTypeController.clearDropDown();
                  }
                });

                return AppDropdown(
                  controller: docTypeController,
                  items: UserProofType.values
                      .map(
                        (e) => DropDownValueModel(name: e.name, value: e.name),
                      )
                      .toList(),
                  title: context.l10n.chooseIdType,
                  onChanged: (value) {
                    if (value is DropDownValueModel && value.value != null) {
                      widget.onTypeChange(value.value as String);
                    }
                  },
                  fillColor: AppColors.ffF8F9FA,
                  hintText: context.l10n.idType,
                );
              },
            )
          else
            AppTextFormField(
              hintText: context.l10n.enterPersonName,
              keyboardType: TextInputType.emailAddress,
              title: context.l10n.chooseIdType,
              controller: TextEditingController(text: widget.docType),
              readOnly: true,
              titleColor: AppColors.black,
              fillColor: AppColors.ffF8F9FA,
              validator: (p0) => nameValidator().call(p0),
            ),
          Padding(
            padding: EdgeInsets.only(top: AppSize.h16, bottom: AppSize.h4),
            child: Text(
              context.l10n.idProofPhoto,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: AppSize.sp14,
                color: AppColors.black,
              ),
            ),
          ),
          GestureDetector(
            onTap: () async {
              final img = await ImagePicker().pickImage(
                source: ImageSource.gallery,
              );
              if (img != null) {
                widget.onImageChange(img.path);
              }
            },
            behavior: HitTestBehavior.opaque,
            child: widget.image.isEmpty
                ? Container(
                    padding: EdgeInsets.symmetric(
                      vertical: AppSize.h30,
                      horizontal: AppSize.h20,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppSize.r10),
                      color: AppColors.ffF2EEF8,
                    ),
                    child: Column(
                      children: [
                        AppAssets.iconsImageAdd.image(
                          height: AppSize.h34,
                          width: AppSize.w34,
                        ),
                        Padding(
                          padding: EdgeInsets.only(
                            top: AppSize.h8,
                            bottom: AppSize.h4,
                          ),
                          child: Text(
                            context.l10n.uploadPicture,
                            textAlign: TextAlign.center,
                            style: context.textTheme.bodyLarge?.copyWith(
                              color: AppColors.ff67509C,
                            ),
                          ),
                        ),
                        Text(
                          context.l10n.tapThisAnd,
                          textAlign: TextAlign.center,
                          style: context.textTheme.bodySmall?.copyWith(
                            fontSize: AppSize.sp10,
                            color: AppColors.ff9A7DCF,
                          ),
                        ),
                      ],
                    ),
                  )
                : SizedBox(
                    height: AppSize.h160,
                    child: Image.file(
                      File(widget.image),
                      errorBuilder: (context, error, stackTrace) => WidgetZoom(
                        heroAnimationTag: widget.title,
                        zoomWidget: AppImage.network(
                          widget.image,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
